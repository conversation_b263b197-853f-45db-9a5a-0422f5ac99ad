@tailwind base;
@tailwind components;
@tailwind utilities;

* {
	box-sizing: border-box;
}

/* App-wide grid layout */
#app {
	height: 100vh;
	width: 100vw;
	max-width: none;
	margin: 0;
	display: grid;
	grid-template-columns: 80px 2fr 0.75fr;
	grid-template-rows: 1fr;
	grid-column-gap: 0px;
	grid-row-gap: 0px;
}

/* Grid column classes for content placement */
.grid-left {
	grid-column: 1;
	background-color: var(--bg-color);
	border-right: 1px solid var(--border-color);
	overflow-y: auto;
}

.grid-center {
	grid-column: 2;
	background-color: var(--bg-color);
	overflow-y: auto;
}

.grid-center-full {
	grid-column: 1 / 3;
	background-color: var(--bg-color);
	overflow-y: auto;
}

.grid-right {
	grid-column: 3;
	background-color: var(--bg-color);
	border-left: 1px solid var(--border-color);
	overflow-y: auto;
}

/* Sidebar content styling */
.sidebar-content {
	padding: 1rem;
	color: var(--text-color);
}

.sidebar-content h3 {
	margin-bottom: 0.5rem;
	font-size: 1.1rem;
	font-weight: 600;
}

.sidebar-content p {
	font-size: 0.9rem;
	line-height: 1.4;
	opacity: 0.8;
}

/* Heroicons compatibility - scoped sizing utilities for SVG icons only */
svg[class*="w-4"] {
	width: 1rem;
}

svg[class*="h-4"] {
	height: 1rem;
}

svg[class*="w-5"] {
	width: 1.25rem;
}

svg[class*="h-5"] {
	height: 1.25rem;
}

svg[class*="w-6"] {
	width: 1.5rem;
}

svg[class*="h-6"] {
	height: 1.5rem;
}

svg[class*="w-8"] {
	width: 2rem;
}

svg[class*="h-8"] {
	height: 2rem;
}

svg[class*="w-12"] {
	width: 3rem;
}

svg[class*="h-12"] {
	height: 3rem;
}

:root {
	font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
	line-height: 1.5;
	font-weight: 400;
	color-scheme: light dark;

	/* Light theme colors (default) */
	--bg-color: #ffffff;
	--bg-color-rgb: 255, 255, 255;
	--text-color: #1a1a1a;
	--message-bg-user: #f0f0f0;
	--message-bg-ai: #ffffff;
	--border-color: #e5e5e5;
	--input-bg: #ffffff;
	--hover-color: #f5f5f5;
	--accent-color: #666666;
	--skeleton-start: rgba(200, 200, 200, 0.1);
	--skeleton-end: rgba(200, 200, 200, 0.3);

	/* shadcn/ui CSS variables */
	--background: 0 0% 100%;
	--foreground: 222.2 84% 4.9%;
	--card: 0 0% 100%;
	--card-foreground: 222.2 84% 4.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 222.2 84% 4.9%;
	--primary: 222.2 47.4% 11.2%;
	--primary-foreground: 210 40% 98%;
	--secondary: 210 40% 96%;
	--secondary-foreground: 222.2 84% 4.9%;
	--muted: 210 40% 96%;
	--muted-foreground: 215.4 16.3% 46.9%;
	--accent: 210 40% 96%;
	--accent-foreground: 222.2 84% 4.9%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 210 40% 98%;
	--border: 214.3 31.8% 91.4%;
	--input: 214.3 31.8% 91.4%;
	--ring: 222.2 84% 4.9%;
	--radius: 0.5rem;
}

/* Dark theme */
:root.dark {
	--bg-color: #030712;
	--bg-color-rgb: 3, 7, 18;
	--text-color: #ffffff;
	--message-bg-user: #111827;
	--message-bg-ai: #0a0f1a;
	--border-color: #10131a;
	--input-bg: #0a0f1a;
	--hover-color: #111827;
	--accent-color: #64748b;
	--skeleton-start: rgba(10, 15, 26, 0.1);
	--skeleton-end: rgba(10, 15, 26, 0.3);

	/* shadcn/ui dark mode CSS variables */
	--background: 222.2 84% 4.9%;
	--foreground: 210 40% 98%;
	--card: 222.2 84% 4.9%;
	--card-foreground: 210 40% 98%;
	--popover: 222.2 84% 4.9%;
	--popover-foreground: 210 40% 98%;
	--primary: 210 40% 98%;
	--primary-foreground: 222.2 47.4% 11.2%;
	--secondary: 217.2 32.6% 17.5%;
	--secondary-foreground: 210 40% 98%;
	--muted: 217.2 32.6% 17.5%;
	--muted-foreground: 215 20.2% 65.1%;
	--accent: 217.2 32.6% 17.5%;
	--accent-foreground: 210 40% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 210 40% 98%;
	--border: 217.2 32.6% 17.5%;
	--input: 217.2 32.6% 17.5%;
	--ring: 212.7 26.8% 83.9%;
}

/* System preference fallback */
@media (prefers-color-scheme: dark) {
	:root:not(.light):not(.dark) {
		--bg-color: #030712;
		--bg-color-rgb: 3, 7, 18;
		--text-color: #ffffff;
		--message-bg-user: #111827;
		--message-bg-ai: #0a0f1a;
		--border-color: #10131a;
		--input-bg: #0a0f1a;
		--hover-color: #111827;
		--accent-color: #64748b;
		--skeleton-start: rgba(10, 15, 26, 0.1);
		--skeleton-end: rgba(10, 15, 26, 0.3);
	}
}

* {
	margin: 0;
	padding: 0;
	scrollbar-width: thin;
	scrollbar-color: rgba(var(--accent-color-rgb), 0.5) transparent;
}

body {
	background-color: var(--bg-color);
	color: var(--text-color);
	min-height: 100vh;
	margin: 0;
}

/* App styles are now handled by the grid layout above */

.chat-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	width: 100%;
	margin: 0;
	padding: 0;
	position: relative;
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
	/* Prevent body scroll when input is focused */
}

/* Chat container styling - full width and height */
.chat-container {
	position: relative;
	height: 100vh;
	width: 100%;
	max-width: 100%;
	margin: 0;
	overflow: hidden;
	background-color: var(--bg-color);
	box-shadow: none;
	border: none;
	transition: none;
	animation: none;
	opacity: 1;
	transform: none;
}

/* Ensure input area works properly within grid layout */
.chat-container .input-area {
	position: sticky;
	bottom: 0;
	z-index: 1001;
	background-color: var(--bg-color);
}





/* Mobile adjustments */
@media (max-width: 768px) {

	/* Mobile adjustments for sticky input */
	.input-area {
		padding-bottom: 1rem;
		padding-left: 16px;
		padding-right: 16px;
		bottom: 28px;
		/* Slightly less space on mobile */
	}

	.message-list {
		padding-bottom: 120px;
		/* Account for input area on mobile */
	}


}

@media (max-width: 480px) {
	.input-area {
		padding-left: 12px;
		padding-right: 12px;
	}


}

/* Responsive grid layout */
@media (max-width: 1024px) {
	#app {
		grid-template-columns: 1fr;
		grid-template-rows: auto auto auto;
	}

	.grid-left,
	.grid-center,
	.grid-center-full,
	.grid-right {
		grid-column: 1;
		border-right: none;
		border-left: none;
	}

	.grid-left {
		grid-row: 1;
	}

	.grid-center {
		grid-row: 2;
		/* Ensure proper spacing for input and disclaimer */
		padding-bottom: 0;
	}

	.grid-right {
		grid-row: 3;
		border-bottom: none;
	}
}

/* Mobile-specific adjustments for bottom navigation */
@media (max-width: 1023px) {
	#app {
		grid-template-rows: auto auto;
		/* Remove left sidebar row on mobile */
	}

	.grid-left {
		display: none;
	}

	.grid-center {
		grid-row: 1;
	}

	.grid-right {
		grid-row: 2;
	}
}

.drag-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	background: radial-gradient(circle, rgba(var(--bg-color-rgb), 0.85) 0%, rgba(var(--bg-color-rgb), 0.95) 100%);
	backdrop-filter: blur(3px);
	animation: fadeIn 0.2s ease-out;
}

.drag-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 0.75rem;
	color: var(--text-color);
	font-size: 1.2rem;
	text-align: center;
	padding: 2.5rem 3rem;
	border-radius: 1rem;
	background-color: rgba(var(--bg-color-rgb), 0.8);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	border: 1px solid var(--border-color);
	max-width: 90%;
	animation: dropMessageAppear 0.4s ease-out;
	position: relative;
	z-index: 10000;
}

.drag-message-icon {
	width: 58px;
	height: 58px;
	color: var(--accent-color);
	margin-bottom: 0.25rem;
}

.drag-message-title {
	font-size: 1.4rem;
	font-weight: 600;
	margin: 0;
	color: var(--text-color);
}

.drag-message-subtitle {
	font-size: 0.9rem;
	opacity: 0.8;
	margin: 0;
	color: var(--accent-color);
}

@keyframes dropMessageAppear {
	0% {
		opacity: 0;
		transform: translateY(10px) scale(0.95);
	}

	100% {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

@keyframes dropIconBounce {

	0%,
	100% {
		transform: translateY(-4px);
	}

	50% {
		transform: translateY(4px);
	}
}

.chat-main {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	overflow: hidden;
	position: relative;
	background-color: var(--bg-color);
}

.message-list-container {
	position: relative;
	flex: 1;
	width: 100%;
}

.message-list {
	flex: 1;
	overflow-y: auto;
	padding: 1rem;
	padding-bottom: 160px;
	/* Add padding to account for sticky input area and disclaimer */
	scroll-behavior: smooth;
	width: 100%;
	scrollbar-width: thin;
	scrollbar-color: transparent transparent;
	transition: scrollbar-color 0.5s ease;
	-webkit-overflow-scrolling: touch;
}

.message-list:hover,
.message-list:focus,
.message-list:active {
	scrollbar-color: var(--accent-color) transparent;
}

/* For WebKit browsers to hide and show scrollbar with animation */
.message-list::-webkit-scrollbar-thumb {
	background-color: transparent;
	transition: background-color 0.5s ease;
}

.message-list:hover::-webkit-scrollbar-thumb,
.message-list:focus::-webkit-scrollbar-thumb,
.message-list:active::-webkit-scrollbar-thumb {
	background-color: var(--accent-color);
}

/* iOS-like auto-hiding scrollbar behavior */
.message-list {
	-webkit-overflow-scrolling: touch;
}

/* Scroll to bottom button */
.scroll-to-bottom-button {
	position: absolute;
	bottom: 80px;
	right: 20px;
	width: 48px;
	height: 48px;
	border-radius: 50%;
	background-color: var(--accent-color);
	color: white;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	transition: all 0.2s ease;
	z-index: 1000;
	opacity: 0;
	transform: translateY(10px);
	animation: fadeInUp 0.3s ease forwards;
}

.scroll-to-bottom-button:hover {
	background-color: var(--brand-color);
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.scroll-to-bottom-button:active {
	transform: translateY(0);
}

@keyframes fadeInUp {
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@media (max-width: 768px) {
	.scroll-to-bottom-button {
		bottom: 100px;
		right: 16px;
		width: 44px;
		height: 44px;
	}
}

.input-area {
	padding-bottom: 2rem;
	padding-left: 1rem;
	padding-right: 1rem;
	background-color: var(--bg-color);
	height: auto;
	display: flex;
	flex-direction: column;
	width: 100%;
	position: sticky;
	bottom: 32px;
	/* Move input area up to make room for disclaimer */
	z-index: 1000;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.input-disclaimer {
	font-size: 0.75rem;
	color: var(--accent-color);
	text-align: center;
	padding: 0.25rem 0;
	opacity: 0.8;
	margin-top: 0.25rem;
}



.input-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	position: relative;
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-top: none;
	border-radius: 1rem;
	padding: 0.75rem;
	min-height: 56px;
	gap: 0.75rem;
	height: auto;
	overflow: visible;
}

.input-preview {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin: 0;
}

.input-preview-item {
	position: relative;
	width: 60px;
	height: 60px;
	border-radius: 0.5rem;
	overflow: hidden;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	background-color: var(--hover-color);
}

.input-preview-item.image-preview {
	width: 60px;
	height: 60px;
}

.input-preview-item.file-preview {
	width: 165px;
	height: 60px;
	display: flex;
	align-items: center;
	padding-right: 20px;
	gap: 0.25rem;
}

.input-preview-item img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.input-preview-item .file-thumbnail {
	width: 60px;
	height: 60px;
	flex-shrink: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--accent-color);
	color: var(--bg-color);
	padding: 0.5rem;
}

.input-preview-item .file-thumbnail svg {
	width: 32px;
	height: 32px;
}

.input-preview-item .file-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 0.25rem;
	padding-right: 0.5rem;
	overflow: hidden;
}

.input-preview-item .file-name {
	font-size: 0.75rem;
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 0.25rem;
}

.input-preview-item .file-ext {
	font-size: 0.6rem;
	color: var(--accent-color);
	text-transform: uppermatter;
	font-weight: 600;
}

.input-preview-remove {
	position: absolute;
	top: 4px;
	right: 4px;
	width: 18px;
	height: 18px;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	line-height: 1;
	padding: 0;
	opacity: 0.9;
	transition: opacity 0.2s;
	z-index: 2;
}

.input-preview-remove svg {
	width: 14px;
	height: 14px;
}

.input-preview-remove:hover {
	opacity: 1;
}

.input-controls-row {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	width: 100%;
	padding: 0.0rem;
}

.input-controls {
	display: flex;
	justify-content: space-between;
	width: 100%;
	align-items: center;
}

/* Left side controls */
.input-left-controls {
	display: flex;
	align-items: center;
}

.file-menu-trigger {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: transparent;
	border: 1px solid var(--accent-color);
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.25rem;
}

.file-menu-trigger:hover {
	background-color: var(--hover-color);
}

.file-menu-icon {
	width: 16px;
	height: 16px;
}

.media-icon {
	width: 16px;
	height: 16px;
}

.media-button {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: transparent;
	border: 1px solid var(--accent-color);
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0.25rem;
	padding: 0;
	transition: all 0.2s ease;
}

.media-button:hover:not(:disabled) {
	background-color: var(--hover-color);
}

.media-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.media-button.recording {
	animation: pulse 1.5s infinite;
	border-color: var(--accent-color);
	background-color: var(--hover-color);
}



.send-controls {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

/* When recording, send controls should take full width */
.send-controls.recording {
	width: 100%;
}

.message {
	display: flex;
	flex-direction: column;
	max-width: 100%;
	margin: 1rem 0;
	padding: 0.5rem 0.75rem;
	border-radius: 0.75rem;
	word-wrap: break-word;
	position: relative;
}

.message.media-only {
	padding: 0;
	margin: 0;
	background: none;
}

.message-user {
	margin-left: auto;
	margin-right: 0;
	background-color: var(--message-bg-user);
	color: var(--text-color);
	width: fit-content;
}

.message-user.media-only {
	margin-left: auto;
	margin-right: 0;
	padding: 0;
	background: none;
}

.message-ai {
	margin-right: 0;
	margin-left: 0;
	background-color: var(--message-bg-ai);
	width: 100%;
	min-height: 48px;
	min-width: 120px;
}

.message-content {
	font-size: 1rem;
	line-height: 1.5;
	min-height: 24px;
}

/* File Upload Styles */
.file-upload {
	display: flex;
	align-items: center;
}

.file-input {
	display: none;
}

.file-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 26px;
	height: 26px;
	padding: 4px;
	background: none;
	border: 1px solid var(--accent-color);
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.2s;
	color: var(--accent-color);
}

.file-button:hover {
	background-color: var(--hover-color);
}

.file-icon {
	width: 20px;
	height: 20px;
}

.file-upload.dragging::after {
	content: '';
	position: absolute;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	border: 2px dashed var(--accent-color);
	border-radius: 8px;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}

	50% {
		transform: scale(1.1);
		opacity: 0.5;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* Message with file attachment styles */
.message-file {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	padding: 0rem;
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	margin: 0.5rem 0;
	width: 100%;
	max-width: 300px;
}

.message-file-icon {
	width: 32px;
	height: 32px;
	padding: 4px;
	color: inherit;
	background-color: var(--hover-color);
	border-radius: 0.25rem;
	flex-shrink: 0;
}

.message-file-info {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.message-file-name {
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.message-file-name a {
	color: var(--text-color);
	text-decoration: none;
}

.message-file-name a:hover {
	text-decoration: underline;
}

.message-file-size {
	font-size: 0.75rem;
	color: var(--accent-color);
}

/* Markdown content styles */
.message-content> :first-child {
	margin-top: 0;
}

.message-content> :last-child {
	margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
	margin: 1.5rem 0 1rem;
	line-height: 1.25;
}

.message-content p {
	margin: 0;
}

.message-content ul,
.message-content ol {
	margin: 0.75rem 0;
	padding-left: 1.5rem;
}

.message-content li {
	margin: 0.25rem 0;
}

.message-content pre {
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	padding: 1rem;
	margin: 0.75rem 0;
	overflow-x: auto;
}

.message-content code {
	font-family: 'SF Mono', Monaco, Menlo, Consolas, 'Ubuntu Mono', monospace;
	font-size: 0.9em;
	padding: 0.2em 0.4em;
	background-color: var(--input-bg);
	border-radius: 0.25rem;
}

.message-content pre code {
	padding: 0;
	background-color: transparent;
}

.message-content blockquote {
	margin: 0.75rem 0;
	padding-left: 1rem;
	border-left: 4px solid var(--border-color);
	color: var(--accent-color);
}

.message-content img {
	max-width: 100%;
	height: auto;
	border-radius: 0.5rem;
	margin: 0.75rem 0;
}

.message-content table {
	width: 100%;
	border-collapse: collapse;
	margin: 0.75rem 0;
}

.message-content th,
.message-content td {
	padding: 0.5rem;
	border: 1px solid var(--border-color);
	text-align: left;
}

.message-content th {
	background-color: var(--input-bg);
}

.message-timestamp {
	font-size: 0.75rem;
	color: #666;
	margin-top: 0.25rem;
	align-self: flex-end;
}

/* Dark mode adjustments for markdown */
:root.dark .message-content blockquote {
	color: var(--accent-color);
}

:root.dark .message-content a {
	color: var(--text-color);
}

:root.dark .message-file-size {
	color: var(--accent-color);
}

/* System preference fallback */
@media (prefers-color-scheme: dark) {
	:root:not(.light):not(.dark) .message-content blockquote {
		color: var(--accent-color);
	}

	:root:not(.light):not(.dark) .message-content a {
		color: var(--text-color);
	}

	:root:not(.light):not(.dark) .message-file-size {
		color: var(--accent-color);
	}
}

/* Loading Indicator Styles */
.loading-indicator {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 0;
	min-height: 24px;
	width: fit-content;
}

.message-ai .loading-indicator {
	margin-left: 0;
}

.dot {
	width: 8px;
	height: 8px;
	background-color: var(--accent-color);
	border-radius: 50%;
	opacity: 0.4;
	animation: loadingDot 1.4s infinite;
}

.dot:nth-child(2) {
	animation-delay: 0.2s;
}

.dot:nth-child(3) {
	animation-delay: 0.4s;
}

@keyframes loadingDot {

	0%,
	100% {
		opacity: 0.4;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.1);
	}
}

/* Media Controls Styles */
.media-controls {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	max-width: fit-content;
}

/* When recording, the media controls should expand to full width */
.media-controls.recording {
	max-width: 100%;
	width: 100%;
}

/* Modal Styles */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-overlay.fullscreen {
	background-color: #000;
}

.modal-content {
	background-color: var(--bg-color);
	border-radius: 1rem;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	width: 90%;
	max-width: 600px;
	max-height: 90vh;
	overflow-y: auto;
	position: relative;
}

.modal-content.fullscreen {
	width: 100%;
	height: 100%;
	max-width: none;
	max-height: none;
	border-radius: 0;
	overflow-y: hidden;
	background-color: #000;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 1.5rem;
	border-bottom: 1px solid var(--border-color);
}

.modal-title {
	font-size: 1.5rem;
	font-weight: 600;
	margin: 0;
}

.modal-close {
	background: none;
	border: none;
	padding: 0.5rem;
	cursor: pointer;
	color: var(--accent-color);
	border-radius: 0.5rem;
	transition: background-color 0.2s;
}

.modal-close:hover {
	background-color: var(--hover-color);
}

.modal-body {
	padding: 1.5rem;
}

.modal-body.fullscreen {
	padding: 0;
	height: 100%;
}

/* Introduction Panel Styles */
.introduction-content {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

.intro-section {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.intro-section h3 {
	font-size: 1.25rem;
	font-weight: 600;
	margin: 0;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.intro-section ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.intro-section li {
	margin: 0.5rem 0;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.intro-section li::before {
	content: "•";
	color: var(--accent-color);
}

kbd {
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.25rem;
	padding: 0.125rem 0.375rem;
	font-size: 0.875rem;
	font-family: monospace;
}

.intro-button {
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.5rem;
	padding: 0.75rem 1.5rem;
	font-size: 1rem;
	font-weight: 600;
	cursor: pointer;
	transition: opacity 0.2s;
	margin-top: 1rem;
	align-self: center;
}

.intro-button:hover {
	opacity: 0.9;
}

/* Update message media styles */
.message-media-container {
	margin: 0.5rem 0;
	border-radius: 0.75rem;
	overflow: hidden;
	max-width: 300px;
	width: 100%;
}

.message-media {
	width: 100%;
	height: auto;
	display: block;
	cursor: pointer;
}

.message.media-only .message-media-container {
	margin: 0;
	border-radius: 0.75rem;
	overflow: hidden;
}

/* Lightbox Styles */
.lightbox-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	cursor: pointer;
	animation: fadeIn 0.3s ease-out;
}

.lightbox-image {
	max-width: 90vw;
	max-height: 90vh;
	object-fit: contain;
	cursor: default;
	animation: zoomIn 0.3s ease-out;
	will-change: transform;
}

.lightbox-close {
	position: fixed;
	top: 1rem;
	right: 1rem;
	background: rgba(0, 0, 0, 0.5);
	border: none;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: white;
	padding: 0.5rem;
	transition: background-color 0.2s, transform 0.2s;
	animation: fadeSlideIn 0.3s ease-out;
}

.lightbox-close:hover {
	background: rgba(0, 0, 0, 0.7);
	transform: scale(1.1);
}

.lightbox-overlay.closing {
	animation: fadeOut 0.2s ease-out forwards;
}

.lightbox-overlay.closing .lightbox-image {
	animation: zoomOut 0.2s ease-out forwards;
}

.lightbox-overlay.closing .lightbox-close {
	animation: fadeSlideOut 0.2s ease-out forwards;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes fadeOut {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
	}
}

@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale(0.95);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes zoomOut {
	from {
		opacity: 1;
		transform: scale(1);
	}

	to {
		opacity: 0;
		transform: scale(0.95);
	}
}

@keyframes fadeSlideIn {
	from {
		opacity: 0;
		transform: translate(20px, -20px);
	}

	to {
		opacity: 1;
		transform: translate(0, 0);
	}
}

@keyframes fadeSlideOut {
	from {
		opacity: 1;
		transform: translate(0, 0);
	}

	to {
		opacity: 0;
		transform: translate(20px, -20px);
	}
}

/* Error Boundary Styles */
.error-boundary {
	padding: 1rem;
	margin: 1rem;
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	background-color: var(--input-bg);
}

.error-boundary h2 {
	color: #e53935;
	margin-bottom: 1rem;
}

.error-boundary details {
	margin: 1rem 0;
}

.error-boundary summary {
	cursor: pointer;
	color: var(--accent-color);
}

.error-boundary pre {
	margin-top: 0.5rem;
	padding: 0.5rem;
	background-color: var(--bg-color);
	border-radius: 0.25rem;
	overflow-x: auto;
}

.error-boundary button {
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.25rem;
	padding: 0.5rem 1rem;
	cursor: pointer;
	transition: opacity 0.2s;
}

.error-boundary button:hover {
	opacity: 0.9;
}

/* Audio Recording UI Styles */
.audio-recording-ui {
	display: flex;
	align-items: center;
	gap: 1rem;
	width: 100%;
	flex: 1;
	padding: 0;
	margin: 0;
	background: none;
	border: none;
	height: 32px;
	animation: fadeSlideIn 0.2s ease-out;
}

@keyframes fadeSlideIn {
	from {
		opacity: 0;
		transform: translateY(4px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.audio-recording-ui.closing {
	animation: fadeSlideOut 0.15s ease-out forwards;
}

@keyframes fadeSlideOut {
	from {
		opacity: 1;
		transform: translateY(0);
	}

	to {
		opacity: 0;
		transform: translateY(4px);
	}
}

.recording-visualization {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 1rem;
	height: 32px;
	animation: expandIn 0.3s ease-out;
	background: transparent;
}

.recording-visualization canvas {
	flex: 1;
	height: 32px;
	border-radius: 0.25rem;
	display: block;
	image-rendering: crisp-edges;
	image-rendering: -webkit-optimize-contrast;
	background: transparent !important;
	background-color: transparent !important;
}

@keyframes expandIn {
	from {
		transform: scaleX(0.97);
		opacity: 0;
	}

	to {
		transform: scaleX(1);
		opacity: 1;
	}
}

.recording-timer {
	font-size: 0.875rem;
	color: var(--accent-color);
	font-variant-numeric: tabular-nums;
	min-width: 40px;
	text-align: right;
}

.recording-control-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	padding: 6px;
	border: none;
	border-radius: 50%;
	background: none;
	cursor: pointer;
	transition: all 0.2s;
	color: var(--accent-color);
	animation: popIn 0.2s ease-out;
}

@keyframes popIn {
	from {
		transform: scale(0.9);
		opacity: 0;
	}

	to {
		transform: scale(1);
		opacity: 1;
	}
}

.recording-control-btn svg {
	stroke-width: 2px;
}

.recording-control-btn.cancel {
	color: var(--text-color);
	background-color: var(--hover-color);
}

.recording-control-btn.confirm {
	color: var(--bg-color);
	background-color: var(--text-color);
}

.recording-control-btn:hover {
	transform: scale(1.05);
}

/* File Menu Styles */
.file-menu {
	position: relative;
	display: flex;
	align-items: center;
}

.file-menu-trigger:hover {
	/* These styles are now handled by the shared selector */
}

.file-menu-dropdown {
	position: absolute;
	bottom: 100%;
	left: 0;
	margin-bottom: 0.5rem;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	padding: 0.25rem;
	min-width: 200px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	animation: slideUp 0.2s ease-out;
	transform-origin: bottom left;
}

.file-menu-dropdown.closing {
	animation: slideDown 0.15s ease-out forwards;
	pointer-events: none;
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(4px) scale(0.95);
	}

	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

@keyframes slideDown {
	from {
		opacity: 1;
		transform: translateY(0) scale(1);
	}

	to {
		opacity: 0;
		transform: translateY(4px) scale(0.95);
	}
}

.file-menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 0.75rem;
	border: none;
	background: none;
	color: var(--text-color);
	cursor: pointer;
	border-radius: 0.25rem;
	transition: all 0.2s;
	animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateX(-4px);
	}

	to {
		opacity: 1;
		transform: translateX(0);
	}
}

.file-menu-item:not(:last-child) {
	border-bottom: 1px solid var(--border-color);
}

.file-menu-item:hover {
	background-color: var(--hover-color);
}

.file-menu-item span {
	font-size: 0.875rem;
}

.file-menu-item svg {
	width: 20px;
	height: 20px;
	margin-left: 0.5rem;
	color: inherit;
}

/* Camera Modal Styles */
.camera-modal {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}

.camera-error {
	padding: 0.75rem;
	background-color: rgba(244, 67, 54, 0.1);
	border: 1px solid rgba(244, 67, 54, 0.5);
	border-radius: 0.5rem;
	color: #f44336;
	font-size: 0.875rem;
	text-align: center;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	max-width: 80%;
}

.camera-preview {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: #000;
	flex-grow: 1;
}

.camera-preview video {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.camera-controls {
	position: absolute;
	bottom: 40px;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 1rem 0;
	z-index: 5;
}

.camera-control-btn {
	background: none;
	border: none;
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;
}

.camera-control-btn.capture {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.8);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
	padding: 0;
	position: relative;
}

.camera-control-btn.capture svg {
	width: 62px;
	height: 62px;
	color: var(--accent-color);
}

.camera-control-btn.capture::before {
	content: '';
	position: absolute;
	top: -4px;
	left: -4px;
	right: -4px;
	bottom: -4px;
	border: 2px solid rgba(255, 255, 255, 0.5);
	border-radius: 50%;
}

.camera-control-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.send-controls {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.message-input {
	flex: 1;
	min-height: 24px;
	padding: 8px 0;
	margin: 0;
	font-size: 1rem;
	line-height: 1.5;
	color: var(--text-color);
	background: none;
	border: none;
	resize: none;
	outline: none;
	overflow: hidden;
	box-sizing: border-box;
	display: block;
	width: 100%;
	z-index: 1;
}

.message-input::placeholder {
	color: var(--accent-color);
	opacity: 1;
}

/* Animation for the drag overlay */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

/* Accessibility - Screen reader only */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}

/* Custom scrollbar for WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
	width: 6px;
	height: 6px;
	background-color: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(var(--accent-color-rgb), 0.5);
	border-radius: 6px;
}

::-webkit-scrollbar-track {
	background-color: transparent;
}

/* iOS-like scrollbar for message list */
.message-list {
	scrollbar-width: thin;
	scrollbar-color: transparent transparent;
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
	overflow-x: hidden;
}

/* Auto-hiding scrollbar behavior */
.message-list::-webkit-scrollbar-thumb {
	opacity: 0;
	transition: opacity 0.3s ease;
	background-color: rgba(var(--accent-color-rgb), 0.5);
}

.message-list.scrolling::-webkit-scrollbar-thumb,
.message-list:hover::-webkit-scrollbar-thumb {
	opacity: 1;
}

/* Improve chat container scrolling */
.chat-container {
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
}

/* Animations for scrollbar */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes fadeOut {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
	}
}

/* Welcome Message */
.welcome-message {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	flex: 1;
	padding: 20px;
}

.welcome-card {
	max-width: 500px;
	background-color: var(--message-bg-ai);
	border-radius: 12px;
	padding: 24px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	text-align: center;
	animation: fadeSlideIn 0.5s ease forwards;
}

/* Mobile responsive adjustments for welcome card */
@media (max-width: 768px) {
	.welcome-card {
		max-width: 100%;
		margin: 0 16px;
		padding: 20px 16px;
		border-radius: 8px;
	}

	.welcome-card h2 {
		font-size: 1.4rem;
		margin-bottom: 10px;
	}

	.welcome-card p {
		font-size: 1rem;
		margin-bottom: 14px;
	}

	.welcome-actions p {
		font-size: 1rem;
		margin-bottom: 14px;
	}

	.welcome-action-button {
		padding: 10px 14px;
		font-size: 0.95rem;
	}
}

@media (max-width: 480px) {
	.welcome-card {
		margin: 0 12px;
		padding: 16px 12px;
	}

	.welcome-card h2 {
		font-size: 1.3rem;
	}

	.welcome-card p {
		font-size: 0.95rem;
	}

	.welcome-actions p {
		font-size: 0.95rem;
	}

	.welcome-action-button {
		padding: 8px 12px;
		font-size: 0.9rem;
	}
}



.welcome-card h2 {
	font-size: 1.6rem;
	font-weight: 600;
	margin-bottom: 12px;
	color: var(--text-color);
}

.welcome-card p {
	font-size: 1.1rem;
	margin-bottom: 16px;
	color: var(--text-secondary);
	line-height: 1.5;
}

.welcome-actions {
	text-align: left;
	border-top: 1px solid rgba(0, 0, 0, 0.08);
	padding-top: 16px;
	margin-top: 8px;
}

.welcome-actions p {
	font-weight: 500;
	margin-bottom: 16px;
	font-size: 1.1rem;
	text-align: center;
}

.welcome-buttons {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.welcome-action-button {
	background-color: var(--bg-color);
	color: var(--text-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px 16px;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	width: 100%;
	text-align: center;
}

.welcome-action-button.primary {
	background-color: var(--text-color);
	color: var(--bg-color);
	border-color: var(--text-color);
}

.welcome-action-button:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-action-button.primary:hover {
	background-color: var(--accent-color);
	border-color: var(--accent-color);
}

/* Dark mode adjustments */
:root.dark .welcome-card {
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
}

:root.dark .welcome-actions {
	border-top-color: rgba(255, 255, 255, 0.08);
}

/* System preference fallback */
@media (prefers-color-scheme: dark) {
	:root:not(.light):not(.dark) .welcome-card {
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
	}

	:root:not(.light):not(.dark) .welcome-actions {
		border-top-color: rgba(255, 255, 255, 0.08);
	}
}

/* Welcome message buttons styles */
.welcome-buttons-container {
	margin-top: 16px;
}

.conversation-header {
	padding: 20px 16px;
	text-align: center;
	background-color: var(--bg-color);
}

.welcome-profile {
	margin-bottom: 16px;
	text-align: center;
	padding: 0 16px;
}

.welcome-buttons {
	display: flex;
	flex-direction: column;
	gap: 8px;
	max-width: 300px;
}

/* Mobile responsive adjustments for welcome buttons */
@media (max-width: 768px) {
	.welcome-buttons {
		max-width: 100%;
	}
}

/* Skeleton Loader */
.skeleton-loader {
	background: linear-gradient(90deg,
			var(--skeleton-start) 0%,
			var(--skeleton-end) 50%,
			var(--skeleton-start) 100%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s ease-in-out infinite;
	opacity: 0.7;
}

@keyframes skeleton-loading {
	0% {
		background-position: 0% 0;
	}

	100% {
		background-position: -200% 0;
	}
}

/* Scoped icon sizing classes */
.icon-w-4 {
	width: 1rem;
	height: 1rem;
}

.icon-w-5 {
	width: 1.25rem;
	height: 1.25rem;
}

.icon-w-6 {
	width: 1.5rem;
	height: 1.5rem;
}

/* Lazy-loading skeleton containers */
.lazy-skeleton-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0.5rem;
}

.mediacontrols-skeleton {
	height: 40px;
	width: 40px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.filemenu-skeleton {
	position: relative;
	display: inline-flex;
}

.modal-skeleton,
.lightbox-skeleton,
.cameramodal-skeleton {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.5);
}



.textarea-wrapper {
	width: 100%;
	position: relative;
	display: block;
	flex: 1;
}

/* Scheduling Components Styles */



/* Date Selector */
.date-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.date-selector-title {
	margin-bottom: 12px;
	text-align: center;
}

.date-selector-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
}

.date-grid {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin-bottom: 16px;
}

.date-row {
	display: flex;
	gap: 8px;
	justify-content: space-between;
}

.date-button {
	flex: 1;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 10px 0;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-width: 70px;
}

.date-button:hover,
.date-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

.date-button.today {
	border-color: var(--accent-color);
	background-color: rgba(var(--accent-color-rgb), 0.1);
}

.date-button-text {
	font-weight: 500;
	font-size: 14px;
}

.today-indicator {
	font-size: 10px;
	color: var(--accent-color);
	margin-top: 4px;
}

.date-selector-footer {
	display: flex;
	justify-content: center;
}

.more-dates-button {
	background-color: transparent;
	border: 1px solid var(--border-color);
	color: var(--accent-color);
	cursor: pointer;
	font-size: 14px;
	padding: 8px 16px;
	border-radius: 16px;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin-top: 8px;
}

.more-dates-button:hover {
	background-color: var(--hover-color);
	text-decoration: underline;
	border-color: var(--accent-color);
}

.more-dates-icon {
	margin-right: 6px;
}

/* Time of Day Selector */
.time-of-day-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.time-selector-title {
	margin-bottom: 12px;
	text-align: center;
}

.time-selector-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
}

.time-of-day-options {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.time-of-day-button {
	display: flex;
	align-items: center;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px;
	cursor: pointer;
	transition: all 0.2s ease;
	width: 100%;
	text-align: left;
}

.time-of-day-button:hover,
.time-of-day-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

.time-of-day-icon {
	margin-right: 12px;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.time-of-day-info {
	flex: 1;
}

.time-of-day-label {
	font-weight: 600;
	font-size: 14px;
	margin-bottom: 4px;
}

.time-of-day-description {
	font-size: 12px;
	color: var(--accent-color);
}

/* Time Slot Selector */
.time-slot-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.time-slot-title {
	margin-bottom: 16px;
	text-align: center;
}

.time-slot-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
	margin-bottom: 4px;
}

.time-slot-subtitle {
	font-size: 14px;
	color: var(--accent-color);
}

.time-slots-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 8px;
	max-height: 300px;
	overflow-y: auto;
	padding-right: 4px;
}

.time-slots-grid::-webkit-scrollbar {
	width: 6px;
}

.time-slots-grid::-webkit-scrollbar-thumb {
	background-color: var(--border-color);
	border-radius: 3px;
}

.time-slots-grid::-webkit-scrollbar-track {
	background-color: transparent;
}

.time-slot-button {
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 10px;
	cursor: pointer;
	transition: all 0.2s ease;
	font-size: 14px;
	font-weight: 500;
	text-align: center;
}

.time-slot-button:hover,
.time-slot-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

/* Schedule Confirmation */
.schedule-confirmation {
	display: flex;
	align-items: center;
	background-color: rgba(var(--accent-color-rgb), 0.1);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px;
	margin: 8px 0;
	animation: fadeIn 0.3s ease forwards;
}

.schedule-confirmation-icon {
	margin-right: 12px;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.confirmation-calendar-icon {
	color: var(--accent-color);
}

.schedule-confirmation-details {
	flex: 1;
}

.schedule-confirmation-title {
	font-weight: 600;
	font-size: 14px;
	margin-bottom: 4px;
	display: flex;
	align-items: center;
}

.schedule-confirmation-date {
	font-size: 14px;
	margin-bottom: 2px;
}

.schedule-confirmation-time {
	font-size: 14px;
	color: var(--accent-color);
}

/* Scheduling Container */
.scheduling-container {
	margin: 12px 0;
	animation: fadeIn 0.3s ease forwards;
}

/* Service selection components */
.service-selection-container {
	margin: 12px 0;
	animation: fadeIn 0.3s ease forwards;
}

.service-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	max-width: 400px;
}

.matter-canvas {
	margin: 8px 0;
	border: 1px solid var(--border-color);
	border-radius: 8px;
	background: var(--bg-color);
	overflow: hidden;
}





.matter-canvas-content {
	padding: 0;
}

.matter-canvas-markdown {
	margin: 0;
	padding: 12px;
	font-family: ui-monospace, 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
	font-size: 13px;
	line-height: 1.5;
	color: var(--text-color);
	background: transparent;
	border: none;
	white-space: pre-wrap;
	word-wrap: break-word;
	overflow-x: auto;
}

/* Missing Information Section */
.missing-info-section {
	margin-top: 12px;
	padding: 12px;
	background: rgba(251, 191, 36, 0.1);
	border: 1px solid rgba(251, 191, 36, 0.3);
	border-radius: 6px;
	border-left: 4px solid #fbbf24;
}

.missing-info-header {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 8px;
	font-weight: 600;
	font-size: 14px;
	color: #92400e;
}

.missing-info-header svg {
	color: #fbbf24;
}

.missing-info-list p {
	margin: 0 0 8px 0;
	font-size: 13px;
	color: var(--text-color);
	line-height: 1.4;
}

.missing-info-list ul {
	margin: 0 0 8px 0;
	padding-left: 20px;
	list-style-type: disc;
}

.missing-info-list li {
	margin: 4px 0;
	font-size: 13px;
	color: var(--text-color);
	line-height: 1.4;
}

.missing-info-note {
	font-size: 12px !important;
	color: var(--accent-color) !important;
	font-style: italic;
	margin-top: 8px !important;
}

/* Feedback UI Styles */
.feedback-ui {
	margin-top: 12px;
	padding: 8px 0;
	border-top: 1px solid var(--border-color);
	opacity: 0.8;
	transition: opacity 0.2s ease;
}

.feedback-ui:hover {
	opacity: 1;
}

.feedback-ui.submitted {
	opacity: 0.6;
}

.feedback-actions {
	display: flex;
	align-items: center;
	gap: 16px;
	flex-wrap: wrap;
}

.feedback-thumbs {
	display: flex;
	gap: 8px;
}

.feedback-btn {
	background: none;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 6px 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	opacity: 0.7;
}

.feedback-btn:hover {
	opacity: 1;
	background: var(--hover-color);
}

.feedback-btn.active {
	opacity: 1;
	background: var(--accent-color);
	color: var(--bg-color);
	border-color: var(--accent-color);
}

.feedback-btn svg {
	width: 16px;
	height: 16px;
}

.feedback-rating {
	display: flex;
	gap: 4px;
}

.feedback-star {
	background: none;
	border: none;
	cursor: pointer;
	padding: 4px;
	color: var(--border-color);
	transition: color 0.2s ease;
}

.feedback-star:hover,
.feedback-star.active {
	color: #fbbf24;
}

.feedback-star svg {
	width: 14px;
	height: 14px;
	fill: currentColor;
}

.feedback-expand-btn {
	background: none;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 6px 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	opacity: 0.7;
}

.feedback-expand-btn:hover {
	opacity: 1;
	background: var(--hover-color);
}

.feedback-expand-btn svg {
	width: 16px;
	height: 16px;
}

.feedback-expanded {
	margin-top: 12px;
	animation: fadeIn 0.2s ease;
}

.feedback-comment {
	width: 100%;
	min-height: 60px;
	padding: 8px 12px;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	background: var(--input-bg);
	color: var(--text-color);
	font-family: inherit;
	font-size: 14px;
	line-height: 1.4;
	resize: vertical;
	transition: border-color 0.2s ease;
}

.feedback-comment:focus {
	outline: none;
	border-color: var(--accent-color);
}

.feedback-comment::placeholder {
	color: var(--accent-color);
	opacity: 0.6;
}

.feedback-submit-actions {
	display: flex;
	gap: 8px;
	margin-top: 8px;
	justify-content: flex-end;
}

.feedback-submit-btn {
	background: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 6px;
	padding: 8px 16px;
	cursor: pointer;
	font-size: 14px;
	transition: all 0.2s ease;
}

.feedback-submit-btn:hover:not(:disabled) {
	opacity: 0.9;
	transform: translateY(-1px);
}

.feedback-submit-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.feedback-cancel-btn {
	background: none;
	color: var(--accent-color);
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 8px 16px;
	cursor: pointer;
	font-size: 14px;
	transition: all 0.2s ease;
}

.feedback-cancel-btn:hover {
	background: var(--hover-color);
}

.feedback-thanks {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: var(--accent-color);
	opacity: 0.8;
}

.feedback-thanks svg {
	width: 16px;
	height: 16px;
	color: #10b981;
}

/* Hide feedback UI on user messages */
.message-user .feedback-ui {
	display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.feedback-actions {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 12px;
		flex-wrap: wrap;
	}

	.feedback-submit-actions {
		flex-direction: column;
		width: 100%;
	}

	.feedback-submit-btn,
	.feedback-cancel-btn {
		width: 100%;
		justify-content: center;
	}
}

/* Team Not Found Styles */
.team-not-found {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 2rem;
	background: var(--bg-color);
}

.team-not-found-content {
	text-align: center;
	max-width: 400px;
	padding: 2rem;
	background: var(--message-bg-ai);
	border-radius: 12px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	border: 1px solid var(--border-color);
}

.team-not-found-icon {
	margin-bottom: 1.5rem;
	color: var(--accent-color);
}

.team-not-found h2 {
	margin-bottom: 1rem;
	color: var(--text-color);
	font-size: 1.5rem;
	font-weight: 600;
}

.team-not-found p {
	margin-bottom: 2rem;
	color: var(--accent-color);
	line-height: 1.6;
}

.team-not-found-actions {
	display: flex;
	gap: 1rem;
	justify-content: center;
	flex-wrap: wrap;
}

.retry-button {
	background: var(--accent-color);
	color: var(--bg-color);
	border: none;
	padding: 0.75rem 1.5rem;
	border-radius: 6px;
	cursor: pointer;
	font-size: 0.875rem;
	font-weight: 500;
	transition: all 0.2s ease;
}

.retry-button:hover {
	opacity: 0.9;
	transform: translateY(-1px);
}

.home-link {
	background: var(--message-bg-user);
	color: var(--text-color);
	border: 1px solid var(--border-color);
	padding: 0.75rem 1.5rem;
	border-radius: 6px;
	text-decoration: none;
	font-size: 0.875rem;
	font-weight: 500;
	transition: all 0.2s ease;
}

.home-link:hover {
	background: var(--hover-color);
	transform: translateY(-1px);
}

/* Team Sidebar Styles */
.team-sidebar {
	padding: 1.5rem;
	color: var(--text-color);
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}



/* Verified Badge */
.verified-check {
	width: 1.25rem;
	height: 1.25rem;
	color: var(--text-color);
	margin-left: 0.25rem;
	flex-shrink: 0;
}

/* Team Profile Component */
.team-profile {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 0.125rem;
}

.team-profile .team-logo-container {
	display: flex;
	justify-content: center;
}

.team-profile .team-logo {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	object-fit: cover;
	border: 3px solid var(--border-color);
	background-color: var(--bg-color);
}

.team-profile .team-logo-placeholder {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	background-color: var(--accent-color);
	color: var(--bg-color);
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3px solid var(--border-color);
}

.team-profile.welcome .team-logo,
.team-profile.welcome .team-logo-placeholder {
	width: 96px;
	height: 96px;
}

.team-profile .team-name {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.25rem;
	flex-wrap: nowrap;
	max-width: 100%;
}

.team-profile .team-name h3 {
	font-size: 1.25rem;
	font-weight: 600;
	text-align: center;
	margin: 0;
	color: var(--text-color);
	line-height: 1.2;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.team-profile.welcome .team-name h3 {
	font-size: 1.5rem;
}

.team-profile .team-slug {
	text-align: center;
	color: var(--text-muted);
	font-size: 0.875rem;
}

/* Team Slug */
.team-slug {
	text-align: center;
}

.team-slug span {
	font-size: 0.9rem;
	color: var(--accent-color);
	font-weight: 500;
}

/* Team Actions */
.team-actions {
	display: flex;
	gap: 0.75rem;
	justify-content: center;
}

.action-button {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1rem;
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.75rem;
	color: var(--text-color);
	font-size: 0.9rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	flex: 1;
	justify-content: center;
}

.action-button:hover {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
}

.action-button svg {
	width: 1rem;
	height: 1rem;
}

/* Team Sections */
.team-section {
	border-top: 1px solid var(--border-color);
	padding-top: 1rem;
}

.section-title {
	font-size: 0.9rem;
	font-weight: 600;
	color: var(--text-color);
	margin: 0 0 0.75rem 0;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.section-content {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.placeholder-text {
	font-size: 0.8rem;
	color: var(--accent-color);
	opacity: 0.7;
	font-style: italic;
}

/* Responsive adjustments for team sidebar */
@media (max-width: 1024px) {
	.team-sidebar {
		padding: 1rem;
		gap: 1rem;
	}

	.team-logo,
	.team-logo-placeholder {
		width: 60px;
		height: 60px;
	}

	.team-name h3 {
		font-size: 1.1rem;
	}

	.verified-check {
		width: 1.125rem;
		height: 1.125rem;
	}

	.team-actions {
		flex-direction: column;
	}

	.action-button {
		justify-content: flex-start;
	}
}

/* Sidebar Matter Canvas Styles */
.team-section .matter-canvas {
	margin: 0;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	background: var(--bg-color);
	overflow: hidden;
	font-size: 12px;
}



.team-section .matter-canvas-toggle {
	padding: 2px;
}

.team-section .matter-canvas-markdown {
	padding: 8px;
	font-size: 11px;
	line-height: 1.4;
	max-height: 300px;
	overflow-y: auto;
}

.team-section .missing-info-section {
	margin-top: 8px;
	padding: 8px;
	font-size: 11px;
}

.team-section .missing-info-header {
	font-size: 12px;
	margin-bottom: 6px;
}

.team-section .missing-info-list p {
	font-size: 11px;
	margin: 0 0 6px 0;
}

.team-section .missing-info-list li {
	font-size: 11px;
	margin: 2px 0;
}

.team-section .missing-info-note {
	font-size: 10px !important;
	margin-top: 6px !important;
}

/* Media Sidebar Styles */
.media-sidebar {
	margin-bottom: 1rem;
}

.media-sidebar .section-title {
	font-size: 0.875rem;
	font-weight: 600;
	color: var(--text-color);
	margin-bottom: 0.75rem;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.empty-media-state {
	text-align: center;
	padding: 1.5rem 0;
	color: var(--accent-color);
}

.empty-icon {
	color: var(--accent-color);
	margin-bottom: 0.5rem;
}

.empty-text {
	font-size: 0.875rem;
	font-weight: 500;
	margin-bottom: 0.25rem;
}

.empty-subtext {
	font-size: 0.75rem;
	opacity: 0.7;
}

.media-groups {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.media-group {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.media-group-title {
	font-size: 0.75rem;
	font-weight: 600;
	color: var(--accent-color);
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.media-files {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.media-file-item {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	padding: 0.5rem;
	border-radius: 0.5rem;
	background-color: var(--hover-color);
	cursor: pointer;
	transition: all 0.2s ease;
}

.media-file-item:hover {
	background-color: var(--border-color);
}

.media-thumbnail {
	flex-shrink: 0;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.image-thumbnail {
	width: 3rem;
	height: 3rem;
	border-radius: 0.375rem;
	overflow: hidden;
	background-color: var(--bg-color);
}

.thumbnail-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.thumbnail-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	transition: opacity 0.2s ease;
}

.media-file-item:hover .thumbnail-overlay {
	opacity: 1;
}

.overlay-icon {
	color: white;
}

.file-thumbnail {
	width: 3rem;
	height: 3rem;
	border-radius: 0.375rem;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.file-icon {
	color: var(--accent-color);
}

.media-file-info {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.file-name {
	font-size: 0.875rem;
	font-weight: 500;
	color: var(--text-color);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.file-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 0.5rem;
}

.file-size {
	font-size: 0.75rem;
	color: var(--accent-color);
}

.download-button {
	padding: 0.25rem;
	border: none;
	background: none;
	color: var(--accent-color);
	cursor: pointer;
	border-radius: 0.25rem;
	transition: all 0.2s ease;
	opacity: 0.7;
}

.download-button:hover {
	background-color: var(--border-color);
	opacity: 1;
}

.download-icon {
	color: var(--accent-color);
}

/* Updated Lightbox Styles */
.lightbox-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease-out;
}

.lightbox-content {
	position: relative;
	max-width: 90vw;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 1rem;
}

.lightbox-image {
	max-width: 100%;
	max-height: 80vh;
	object-fit: contain;
	border-radius: 0.5rem;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.lightbox-video-container {
	max-width: 100%;
	max-height: 80vh;
	border-radius: 0.5rem;
	overflow: hidden;
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.lightbox-video {
	width: 100%;
	height: auto;
	max-height: 80vh;
}

.video-placeholder {
	position: relative;
	cursor: pointer;
	max-width: 100%;
	max-height: 80vh;
}

.video-preview {
	width: 100%;
	height: auto;
	max-height: 80vh;
	object-fit: contain;
}

.video-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
}

.play-icon {
	color: white;
}

.play-text {
	color: white;
	font-size: 0.875rem;
	font-weight: 500;
}

.lightbox-info {
	text-align: center;
	color: white;
}

.lightbox-title {
	font-size: 1.125rem;
	font-weight: 600;
	margin-bottom: 0.25rem;
}

.lightbox-meta {
	font-size: 0.875rem;
	opacity: 0.8;
}

.lightbox-close {
	position: absolute;
	top: 1rem;
	right: 1rem;
	width: 3rem;
	height: 3rem;
	border: none;
	background-color: rgba(0, 0, 0, 0.5);
	color: white;
	border-radius: 50%;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
	z-index: 10000;
}

.lightbox-close:hover {
	background-color: rgba(0, 0, 0, 0.7);
}

.lightbox-overlay.closing {
	animation: fadeOut 0.3s ease-in;
}

.lightbox-overlay.closing .lightbox-content {
	animation: zoomOut 0.3s ease-in;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.media-file-item {
		padding: 0.375rem;
		gap: 0.5rem;
	}

	.image-thumbnail,
	.file-thumbnail {
		width: 2.5rem;
		height: 2.5rem;
	}

	.file-name {
		font-size: 0.8125rem;
	}

	.file-size {
		font-size: 0.6875rem;
	}

	.lightbox-content {
		max-width: 95vw;
		max-height: 95vh;
	}

	.lightbox-close {
		top: 0.5rem;
		right: 0.5rem;
		width: 2.5rem;
		height: 2.5rem;
	}
}

/* Simple Privacy & Support Links */
.privacy-support-links {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.privacy-support-link {
	color: var(--accent-color);
	text-decoration: none;
	font-size: 0.875rem;
	padding: 0.25rem 0;
}

.privacy-support-link:hover {
	text-decoration: underline;
}

/* Mobile adjustments for team profile */
@media (max-width: 768px) {
	.team-profile .team-name h3 {
		font-size: 1rem;
		line-height: 1.1;
	}

	.verified-check {
		width: 1rem;
		height: 1rem;
	}

	.team-profile .team-name {
		gap: 0.125rem;
	}
}

/* Left Sidebar Styles */
.left-sidebar {
	background: var(--sidebar-bg);
	border-right: 1px solid var(--border-color);
	height: 100vh;
	overflow-y: auto;
}

.left-sidebar-content {
	padding: 1rem;
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	height: 100%;
}

.left-sidebar-section {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.left-sidebar-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 1rem;
	border-radius: 0.375rem;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	gap: 0.25rem;
}

.left-sidebar-header:hover {
	background: var(--hover-color);
}

.left-sidebar-header.active {
	color: var(--accent-color);
	background: var(--hover-color);
}

.left-sidebar-icon {
	width: 2rem;
	height: 2rem;
	color: var(--text-color);
	display: block !important;
	opacity: 1 !important;
	visibility: visible !important;
}

.left-sidebar-title {
	font-size: 0.875rem;
	font-weight: 600;
	color: var(--text-color);
	margin: 0;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.left-sidebar-content {
	padding: 1rem;
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	height: 100%;
}

.left-sidebar-top {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.left-sidebar-bottom {
	margin-top: auto;
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 60px;
	background: var(--bg-color);
	border-top: 1px solid var(--border-color);
	display: none;
	justify-content: space-around;
	align-items: center;
	z-index: 1000;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.bottom-nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
	height: 100%;
	background: none;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	opacity: 0.7;
	gap: 0.25rem;
	max-width: 200px;
	/* Limit width for better spacing with 2 items */
}

.bottom-nav-item:hover {
	opacity: 1;
	background: var(--hover-color);
}

.bottom-nav-item.active {
	opacity: 1;
	color: var(--accent-color);
}

.bottom-nav-icon {
	width: 1.5rem;
	height: 1.5rem;
}

.bottom-nav-label {
	font-size: 0.75rem;
	font-weight: 500;
	line-height: 1;
}

/* Responsive behavior */
@media (max-width: 1023px) {
	.mobile-bottom-nav {
		display: flex;
	}

	.left-sidebar {
		display: none;
	}

	.chat-container {
		padding-bottom: 60px;
	}

	.input-area {
		bottom: 92px;
		/* 60px bottom nav + 32px disclaimer */
	}

	.message-list {
		padding-bottom: 180px;
		/* Account for bottom nav + input area + disclaimer */
	}
}

/* Mobile Sidebar Slide-out */
.mobile-sidebar-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 2000;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	backdrop-filter: blur(2px);
	-webkit-backdrop-filter: blur(2px);
}

.mobile-sidebar-overlay.open {
	opacity: 1;
	visibility: visible;
}

.mobile-sidebar-panel {
	position: fixed;
	top: 0;
	right: -100%;
	width: 85%;
	max-width: 400px;
	height: 100%;
	background: var(--bg-color);
	border-left: 1px solid var(--border-color);
	transition: right 0.3s ease;
	z-index: 2001;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.mobile-sidebar-panel.open {
	right: 0;
}

.mobile-sidebar-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 1rem;
	border-bottom: 1px solid var(--border-color);
	background: var(--bg-color);
	flex-shrink: 0;
}

.mobile-sidebar-title {
	font-size: 1.125rem;
	font-weight: 600;
	margin: 0;
	color: var(--text-color);
}

.mobile-sidebar-close {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 2.5rem;
	height: 2.5rem;
	border: none;
	background: none;
	color: var(--text-color);
	cursor: pointer;
	border-radius: 0.375rem;
	transition: all 0.2s ease;
}

.mobile-sidebar-close:hover {
	background: var(--hover-bg);
}

.mobile-sidebar-content {
	flex: 1;
	overflow-y: auto;
	padding: 1rem;
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

.mobile-sidebar-section {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

/* Mobile sidebar specific adjustments */
.mobile-sidebar-content .team-profile {
	padding: 0;
	border: none;
	background: none;
}

.mobile-sidebar-content .team-actions {
	padding: 0;
}

.mobile-sidebar-content .team-section {
	padding: 0;
	border: none;
	background: none;
}

.mobile-sidebar-content .section-title {
	font-size: 1rem;
	margin-bottom: 0.5rem;
}

/* Hide right sidebar on mobile when slide-out is available */
@media (max-width: 1023px) {
	.grid-right {
		display: none;
	}
}

/* Mobile Team Profile Button */
.mobile-team-profile-button {
	display: none;
	position: sticky;
	top: 0;
	z-index: 100;
	background: var(--bg-color);
	border-bottom: 1px solid var(--border-color);
	padding: 0.75rem 1rem;
}

.mobile-profile-trigger {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	background: none;
	border: none;
	cursor: pointer;
	color: var(--text-color);
	width: 100%;
	text-align: left;
	padding: 0.5rem;
	border-radius: 0.5rem;
	transition: all 0.2s ease;
}

.mobile-profile-trigger:hover {
	background: var(--hover-bg);
}

.mobile-profile-image {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 50%;
	object-fit: cover;
}

.mobile-profile-name {
	font-size: 1rem;
	font-weight: 600;
	color: var(--text-color);
}

@media (max-width: 1023px) {
	.mobile-team-profile-button {
		display: block;
	}
}

/* Mobile Top Navigation */
.mobile-top-nav {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 60px;
	background: var(--bg-color);
	border-bottom: 1px solid var(--border-color);
	display: none;
	align-items: center;
	justify-content: space-between;
	padding: 0 1rem;
	z-index: 1000;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.mobile-top-nav-profile {
	display: flex;
	align-items: center;
	background: none;
	border: none;
	cursor: pointer;
	color: var(--text-color);
	padding: 0.5rem;
	border-radius: 0.5rem;
	transition: all 0.2s ease;
	flex: 1;
	text-align: left;
}

.mobile-top-nav-profile:hover {
	background: var(--hover-bg);
}

.mobile-top-nav-team {
	display: flex;
	align-items: center;
	gap: 0.75rem;
}

.mobile-top-nav-image {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 50%;
	object-fit: cover;
	flex-shrink: 0;
}

.mobile-top-nav-info {
	display: flex;
	flex-direction: column;
	gap: 0.125rem;
	min-width: 0;
}

.mobile-top-nav-name {
	font-size: 1rem;
	font-weight: 600;
	color: var(--text-color);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.mobile-top-nav-status {
	font-size: 0.75rem;
	color: var(--accent-color);
	font-weight: 500;
}

.mobile-top-nav-menu {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 2.5rem;
	height: 2.5rem;
	background: none;
	border: none;
	color: var(--text-color);
	cursor: pointer;
	border-radius: 0.375rem;
	transition: all 0.2s ease;
	flex-shrink: 0;
}

.mobile-top-nav-menu:hover {
	background: var(--hover-bg);
}

/* Theme Toggle Button */
.theme-toggle {
	display: flex;
	align-items: center;
	justify-content: center;
	background: none;
	border: none;
	padding: 8px;
	border-radius: 8px;
	color: var(--text-color);
	cursor: pointer;
	transition: all 0.2s ease;
	opacity: 0.8;
}

.theme-toggle:hover {
	background-color: var(--hover-color);
	opacity: 1;
}

.theme-toggle:focus {
	outline: 2px solid var(--accent-color);
	outline-offset: 2px;
}

/* Show top nav on mobile, hide team profile button */
@media (max-width: 1023px) {
	.mobile-top-nav {
		display: flex;
	}

	.mobile-team-profile-button {
		display: none;
	}

	.chat-container {
		padding-top: 60px;
	}

	.message-list {
		padding-top: 0;
	}
}

/* Matter Components Styles */
.matters-list-container {
	padding: 1rem;
	height: 100%;
	overflow-y: auto;
}

.matters-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.5rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid var(--border-color);
}

.matters-title {
	font-size: 1.5rem;
	font-weight: 600;
	color: var(--text-color);
	margin: 0;
}

.create-matter-button {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.5rem 1rem;
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s;
}

.create-matter-button:hover {
	opacity: 0.9;
	transform: translateY(-1px);
}

.create-matter-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
	transform: none;
}

.matters-loading {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
}

.matters-empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 3rem 1rem;
	color: var(--accent-color);
}

.empty-icon {
	width: 4rem;
	height: 4rem;
	margin-bottom: 1rem;
	opacity: 0.5;
}

.empty-title {
	font-size: 1.25rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
	color: var(--text-color);
}

.empty-description {
	font-size: 0.875rem;
	margin-bottom: 1.5rem;
	opacity: 0.8;
	max-width: 300px;
}

.empty-create-button {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1.5rem;
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s;
}

.empty-create-button:hover {
	opacity: 0.9;
	transform: translateY(-1px);
}

.matters-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 1rem;
}

.matter-card {
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 0.75rem;
	padding: 1rem;
	cursor: pointer;
	transition: all 0.2s;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.matter-card:hover {
	border-color: var(--accent-color);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

.matter-card:focus {
	outline: 2px solid var(--accent-color);
	outline-offset: 2px;
}

.matter-card-header {
	margin-bottom: 0.75rem;
}

.matter-card-title {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 0.5rem;
}

.matter-title {
	font-size: 1rem;
	font-weight: 600;
	color: var(--text-color);
	margin: 0;
	flex: 1;
}

.matter-status {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	padding: 0.25rem 0.5rem;
	border-radius: 0.375rem;
	font-size: 0.75rem;
	font-weight: 500;
	white-space: nowrap;
}

.matter-card-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 0.75rem;
	color: var(--accent-color);
}

.matter-service {
	font-weight: 500;
}

.matter-date {
	opacity: 0.8;
}

.matter-card-content {
	margin-bottom: 0.75rem;
}

.matter-summary {
	font-size: 0.875rem;
	line-height: 1.4;
	color: var(--text-color);
	margin: 0;
	opacity: 0.9;
}

.matter-card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 0.75rem;
	border-top: 1px solid var(--border-color);
}

.quality-score {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.quality-label {
	font-size: 0.75rem;
	color: var(--accent-color);
}

.quality-badge {
	padding: 0.125rem 0.375rem;
	border-radius: 0.25rem;
	font-size: 0.75rem;
	font-weight: 600;
}

.quality-excellent {
	background-color: #dcfce7;
	color: #166534;
}

.quality-good {
	background-color: #dbeafe;
	color: #1e40af;
}

.quality-fair {
	background-color: #fef3c7;
	color: #92400e;
}

.quality-poor {
	background-color: #fee2e2;
	color: #991b1b;
}

.urgency-badge {
	padding: 0.125rem 0.375rem;
	background-color: var(--hover-color);
	color: var(--accent-color);
	border-radius: 0.25rem;
	font-size: 0.75rem;
	font-weight: 500;
}

/* Matter Detail Styles */
.matter-detail-container {
	padding: 1rem;
	height: 100%;
	overflow-y: auto;
}

.matter-detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid var(--border-color);
}

.back-button {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.5rem 1rem;
	background-color: transparent;
	color: var(--accent-color);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	font-size: 0.875rem;
	cursor: pointer;
	transition: all 0.2s;
}

.back-button:hover {
	background-color: var(--hover-color);
}

.matter-detail-actions {
	display: flex;
	gap: 0.5rem;
}

.edit-button {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.5rem 1rem;
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.5rem;
	font-size: 0.875rem;
	cursor: pointer;
	transition: all 0.2s;
}

.edit-button:hover {
	opacity: 0.9;
}

.matter-detail-content {
	max-width: 800px;
}

.matter-detail-title {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 1.5rem;
}

.matter-detail-title .matter-title {
	font-size: 1.75rem;
	font-weight: 700;
	color: var(--text-color);
	margin: 0;
}

.matter-detail-title .matter-status {
	padding: 0.5rem 1rem;
	font-size: 0.875rem;
}

.matter-detail-meta {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 1rem;
	margin-bottom: 2rem;
	padding: 1rem;
	background-color: var(--hover-color);
	border-radius: 0.5rem;
}

.meta-item {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.meta-label {
	font-size: 0.75rem;
	color: var(--accent-color);
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.05em;
}

.meta-value {
	font-size: 0.875rem;
	color: var(--text-color);
	font-weight: 500;
}

.meta-value.urgency {
	color: #dc2626;
	font-weight: 600;
}

.section-title {
	font-size: 1.125rem;
	font-weight: 600;
	color: var(--text-color);
	margin: 0 0 1rem 0;
	padding-bottom: 0.5rem;
	border-bottom: 1px solid var(--border-color);
}

.matter-quality-section,
.matter-summary-section,
.matter-contact-section,
.matter-answers-section {
	margin-bottom: 2rem;
}

.quality-score-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1rem;
	background-color: var(--hover-color);
	border-radius: 0.5rem;
}

.quality-score-main {
	display: flex;
	align-items: center;
	gap: 1rem;
}

.quality-score-value {
	font-size: 1.5rem;
	font-weight: 700;
	color: var(--text-color);
}

.quality-score-badge {
	padding: 0.25rem 0.75rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	font-weight: 600;
}

.quality-score-details {
	text-align: right;
}

.quality-urgency {
	font-size: 0.875rem;
	color: var(--accent-color);
}

.matter-summary-content {
	padding: 1rem;
	background-color: var(--hover-color);
	border-radius: 0.5rem;
	font-size: 0.875rem;
	line-height: 1.6;
	color: var(--text-color);
}

.contact-info {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.contact-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem;
	background-color: var(--hover-color);
	border-radius: 0.5rem;
}

.contact-label {
	font-size: 0.875rem;
	color: var(--accent-color);
	font-weight: 500;
}

.contact-value {
	font-size: 0.875rem;
	color: var(--text-color);
	font-weight: 500;
}

.answers-list {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.answer-item {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	padding: 0.75rem;
	background-color: var(--hover-color);
	border-radius: 0.5rem;
}

.answer-question {
	font-size: 0.875rem;
	color: var(--accent-color);
	font-weight: 500;
}

.answer-value {
	font-size: 0.875rem;
	color: var(--text-color);
	line-height: 1.4;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
	.matters-grid {
		grid-template-columns: 1fr;
	}

	.matter-detail-header {
		flex-direction: column;
		align-items: stretch;
		gap: 1rem;
	}

	.matter-detail-title {
		flex-direction: column;
		align-items: flex-start;
		gap: 1rem;
	}

	.matter-detail-meta {
		grid-template-columns: 1fr;
	}

	.quality-score-display {
		flex-direction: column;
		align-items: flex-start;
		gap: 1rem;
	}

	.contact-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 0.25rem;
	}
}