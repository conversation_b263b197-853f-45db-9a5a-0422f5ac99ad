import {
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  Bars3Icon
} from '@heroicons/react/24/solid';
import ThemeToggle from './ThemeToggle';
import { Button } from './ui/button';

interface LeftSidebarProps {
  currentRoute: string;
  onTabChange: (tab: 'chats' | 'matters') => void;
  onOpenMenu?: () => void;
}

const LeftSidebar = ({ currentRoute, onTabChange, onOpenMenu }: LeftSidebarProps) => {
  return (
    <div className="left-sidebar">
      <div className="left-sidebar-content">
        {/* Top Section */}
        <div className="left-sidebar-top">
          {/* Chats Section */}
          <div className="left-sidebar-section">
            <Button
              variant="secondary"
              onClick={() => onTabChange('chats')}
              className={`w-full flex-col h-auto py-3 ${currentRoute === 'chats' ? 'bg-accent' : ''}`}
            >
              <ChatBubbleLeftRightIcon className="icon-w-6 mb-1" />
              <span style={{ fontSize: '0.75rem' }}>Chats</span>
            </Button>
          </div>

          {/* Matters Section */}
          <div className="left-sidebar-section">
            <Button
              variant="secondary"
              onClick={() => onTabChange('matters')}
              className={`w-full flex-col h-auto py-3 ${currentRoute === 'matters' ? 'bg-accent' : ''}`}
            >
              <DocumentTextIcon className="icon-w-6 mb-1" />
              <span style={{ fontSize: '0.75rem' }}>Matters</span>
            </Button>
          </div>
        </div>

        {/* Bottom Section - Theme Toggle and Menu */}
        <div className="left-sidebar-bottom">
          <div className="left-sidebar-section" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '0.5rem' }}>
            <ThemeToggle />
            <Button
              variant="secondary"
              onClick={onOpenMenu}
              className="w-full flex-col h-auto py-3"
            >
              <Bars3Icon className="icon-w-6 mb-1" />
              <span style={{ fontSize: '0.75rem' }}>Menu</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeftSidebar; 